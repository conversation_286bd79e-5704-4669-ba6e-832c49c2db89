from torch_geometric.datasets import Planetoid
import torch

dataset=Planetoid(root='.',name='<PERSON>')

data=dataset[0]

print(f'{dataset=}')
print('Number of graphs:',len(dataset))
print('Number of nodes:',data.x.shape[0])
print('Number of features:',dataset.num_features)
print('Number of classes:',dataset.num_classes)

def accuracy(y_pred,y_true):
    return torch.sum(y_pred==y_true)/len(y_true)

from torch.nn import Linear
import torch.nn.functional as F


class GNN(torch.nn.Module):
    
    def __init__(self, dim_in,dim_h,dim_out):
        super().__init__()
        self.linear1=Linear(dim_in,dim_h)
        self.linear2=Linear(dim_h,dim_out)

    def forward(self,x):
        x=self.linear1(x)
        x=torch.relu(x)
        x=self.linear2(x)
        return F.log_softmax(x,dim=1)
    
    def fit(self,data,epochs):
        criterion=torch.nn.CrossEntropyLoss()
        optimizer=torch.optim.Adam(self.parameters(),lr=0.01)
        self.train()
        for epoch in range(epochs+1):
            optimizer.zero_grad()
            out=self(data.x)
            loss=criterion(out[data.train_mask],data.y[data.train_mask])
            acc = accuracy(out[data.train_mask].argmax(dim=1), data.y[data.train_mask])
            loss.backward()
            optimizer.step()
            if epoch %10==0:
                val_loss=criterion(out[data.val_mask],data.y[data.val_mask])
                val_acc=accuracy(out[data.val_mask].argmax(dim=1),data.y[data.val_mask])
                print('Epoch:',epoch)
                print('Train loss:',loss)
                print('Train accuracy:',(acc*100))
                print('Validation loss:',val_loss)
                print('Validation accuracy:',(val_acc*100))
        
    def test(self,data):
        self.eval()
        out=self(data.x)
        acc=accuracy(out[data.test_mask].argmax(dim=1),data.y[data.test_mask])
        return acc

    pass

mlp=GNN(dataset.num_features,16,dataset.num_classes)
print(mlp)

mlp.fit(data,100)
mlp.test(data)

class VanillaGNNLayer(torch.nn.Module):

    def __init__(self,dim_in,dim_out):
        super().__init__()
        self.linear=Linear(dim_in,dim_out,bias=False)

    def forward(self,x,adjacency):
        x=self.linear(x)
        x=torch.sparse.mm(adjacency,x)
        return x

from torch_geometric.utils import to_dense_adj

adjacency= to_dense_adj(data.edge_index)[0]
adjacency+=torch.eye(len(adjacency))


class VanillaGNN(torch.nn.Module):
    
    def __init__(self, dim_in,dim_h,dim_out):
        super().__init__()
        self.gnn1=VanillaGNNLayer(dim_in,dim_h)
        self.gnn2=VanillaGNNLayer(dim_h,dim_out)

    def forward(self,x,adjacency):
        h=self.gnn1(x,adjacency)
        h=torch.relu(h)
        h=self.gnn2(h,adjacency)
        return F.log_softmax(h,dim=1)
    
    def fit(self,data,epochs):
        criterion=torch.nn.CrossEntropyLoss()
        optimizer=torch.optim.Adam(self.parameters(),lr=0.01)
        self.train()
        for epoch in range(epochs+1):
            optimizer.zero_grad()
            out=self(data.x,adjacency)
            loss=criterion(out[data.train_mask],data.y[data.train_mask])
            acc = accuracy(out[data.train_mask].argmax(dim=1), data.y[data.train_mask])
            loss.backward()
            optimizer.step()
            if epoch %10==0:
                val_loss=criterion(out[data.val_mask],data.y[data.val_mask])
                val_acc=accuracy(out[data.val_mask].argmax(dim=1),data.y[data.val_mask])
                print('Epoch:',epoch)
                print('Train loss:',loss)
                print('Train accuracy:',(acc*100))
                print('Validation loss:',val_loss)
                print('Validation accuracy:',(val_acc*100))
        
    def test(self,data):
        self.eval()
        out=self(data.x,adjacency)
        acc=accuracy(out[data.test_mask].argmax(dim=1),data.y[data.test_mask])
        return acc

    pass

gnn = VanillaGNN(dataset.num_features, 16, dataset.num_classes)
print(gnn)
gnn.fit(data, epochs=100)
acc = gnn.test(data)
print(f'\nGNN test accuracy: {acc*100:.2f}%')

from torch_geometric.nn import GCNConv

class GCN(torch.nn.Module):    
    def __init__(self, dim_in,dim_h,dim_out):
        super().__init__()
        self.gnn1=GCNConv(dim_in,dim_h)
        self.gnn2=GCNConv(dim_h,dim_out)

    def forward(self,x,edge_index):
        h=self.gnn1(x,edge_index)
        h=torch.relu(h)
        h=self.gnn2(h,edge_index)
        return F.log_softmax(h,dim=1)
    
    def fit(self,data,epochs):
        criterion=torch.nn.CrossEntropyLoss()
        optimizer=torch.optim.Adam(self.parameters(),lr=0.01,weight_decay=5e-4)
        self.train()
        for epoch in range(epochs+1):
            optimizer.zero_grad()
            out=self(data.x,data.edge_index)
            loss=criterion(out[data.train_mask],data.y[data.train_mask])
            acc = accuracy(out[data.train_mask].argmax(dim=1), data.y[data.train_mask])
            loss.backward()
            optimizer.step()
            if epoch %10==0:
                val_loss=criterion(out[data.val_mask],data.y[data.val_mask])
                val_acc=accuracy(out[data.val_mask].argmax(dim=1),data.y[data.val_mask])
                print('Epoch:',epoch)
                print('Train loss:',loss)
                print('Train accuracy:',(acc*100))
                print('Validation loss:',val_loss)
                print('Validation accuracy:',(val_acc*100))
        
    def test(self,data):
        self.eval()
        out=self(data.x,data.edge_index)
        acc=accuracy(out[data.test_mask].argmax(dim=1),data.y[data.test_mask])
        return acc

    pass

gcn = GCN(dataset.num_features, 16, dataset.num_classes)
print(gcn)
gcn.fit(data, epochs=100)

acc = gcn.test(data)
print(f'GCN test accuracy: {acc*100:.2f}%')

import torch
torch.manual_seed(1)
import torch.nn.functional as F
from torch_geometric.nn import GATv2Conv, GCNConv
from torch.nn import Linear, Dropout


def accuracy(y_pred, y_true):
    """Calculate accuracy."""
    return torch.sum(y_pred == y_true) / len(y_true)


class GAT(torch.nn.Module):
    def __init__(self, dim_in, dim_h, dim_out, heads=8):
        super().__init__()
        self.gat1 = GATv2Conv(dim_in, dim_h, heads=heads)
        self.gat2 = GATv2Conv(dim_h*heads, dim_out, heads=1)

    def forward(self, x, edge_index):
        h = F.dropout(x, p=0.6, training=self.training)
        h = self.gat1(h, edge_index)
        h = F.elu(h)
        h = F.dropout(h, p=0.6, training=self.training)
        h = self.gat2(h, edge_index)
        return F.log_softmax(h, dim=1)

    def fit(self, data, epochs):
        criterion = torch.nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(self.parameters(), lr=0.01, weight_decay=0.01)

        self.train()
        for epoch in range(epochs+1):
            optimizer.zero_grad()
            out = self(data.x, data.edge_index)
            loss = criterion(out[data.train_mask], data.y[data.train_mask])
            acc = accuracy(out[data.train_mask].argmax(dim=1), data.y[data.train_mask])
            loss.backward()
            optimizer.step()

            if(epoch % 20 == 0):
                val_loss = criterion(out[data.val_mask], data.y[data.val_mask])
                val_acc = accuracy(out[data.val_mask].argmax(dim=1), data.y[data.val_mask])
                print(f'Epoch {epoch:>3} | Train Loss: {loss:.3f} | Train Acc: {acc*100:>5.2f}% | Val Loss: {val_loss:.2f} | Val Acc: {val_acc*100:.2f}%')

    @torch.no_grad()
    def test(self, data):
        self.eval()
        out = self(data.x, data.edge_index)
        acc = accuracy(out.argmax(dim=1)[data.test_mask], data.y[data.test_mask])
        return acc

# Create the Vanilla GNN model
gat = GAT(dataset.num_features, 32, dataset.num_classes)
print(gat)

# Train
gat.fit(data, epochs=100)

# Test
acc = gat.test(data)
print(f'GAT test accuracy: {acc*100:.2f}%')



from torch_geometric.datasets import Planetoid
dataset = Planetoid(root=".", name="Cora")
data = dataset[0]

gat = GAT(dataset.num_features, 32, dataset.num_classes)
gat.fit(data, epochs=100)

acc = gat.test(data)
print(f'GAT test accuracy: {acc*100:.2f}%')

