import qmpy_rester as qr

chalcogens = ['S', 'Se', 'Te']
all_data = []

with qr.QMPYRester() as q:
    for chalcogen in chalcogens:
        comp = f'W{chalcogen}2'  # e.g. WS2, WSe2, WTe2
        data = q.get_oqmd_phases(composition=comp, confirm=True)  # added confirm=False to avoid prompts
        all_data.extend(data)





import qmpy_rester as qr

## Return list of data
with qr.QMPYRester() as q:
    kwargs = {
        'elements': 'Fe,Mn',                    # include element Fe and Mn
        'nelements': '<5',                      # less than 4 element species in the compound
        '_oqmd_stability': '<0',                # stability calculted by oqmd is less than 0
        }
    list_of_data = q.get_optimade_structures(**kwargs)

list_of_data

