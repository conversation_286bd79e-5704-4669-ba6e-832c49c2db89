% % \documentclass[conference]{IEEEtran}
% % \IEEEoverridecommandlockouts
% % \usepackage{cite}
% % \usepackage{booktabs}
% % \usepackage{adjustbox}
% % \usepackage{hyperref} % Enables clickable links
% % \usepackage{amsmath,amssymb,amsfonts}
% % \usepackage{algorithmic}
% % \usepackage{graphicx}
% % \usepackage{textcomp}
% % \usepackage{xcolor}
% % \def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
% %     T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
% % \begin{document}

% % \title{Crystal Systems Classification Using Machine Learning for Phosphate Based Cathode Materials in Lithium-Ion Battery \\

% % \thanks{}
% % }

% % \author{\IEEEauthorblockN{1\textsuperscript{st} Yogesh Yadav}
% % \IEEEauthorblockA{\textit{Dept.Physics} \\
% % \textit{Indian Institute of Technology Jodhpur }\\
% % Jodhpur, India \\
% % <EMAIL> }
% % \and
% % \IEEEauthorblockN{\textsuperscript{} Dr. Sandeep K Yadav}
% % \IEEEauthorblockA{\textit{Dept. Electrical Engineering } \\
% % \textit{Indian Institute of Technology Jodhpur}\\
% % Jodhpur, India \\<EMAIL>
% % }
% % \and
% % \IEEEauthorblockN{\textsuperscript{} Dr. Vivek Vijay}
% % \IEEEauthorblockA{\textit{Dept. Mathematics} \\
% % \textit{Indian Institute of Technology Jodhpur}\\
% % Jodhpur, India \\<EMAIL>
% % }
% % \and
% % \IEEEauthorblockN{\textsuperscript{} Dr. Ambesh Dixit}
% % \IEEEauthorblockA{\textit{Dept. Physics} \\
% % \textit{Indian Institute of Technology Jodhpur}\\
% %  Jodhpur, India\\
% % <EMAIL>}
% % }
% % \maketitle

% % \begin{abstract}The physical and chemical characteristics of cathodes used in batteries are derived from the lithium-ion phosphate cathodes' crystalline arrangement, which is pivotal to the overall battery performance. Therefore, the correct prediction of the crystal system is essential to estimate the properties of cathodes. This study investigates machine learning classification algorithms for predicting the crystal systems, namely monoclinic, orthorhombic, and triclinic, related to Li–P– (Mn, Fe, Co, Ni, V)–O based Phosphate cathodes. The data used in this work is extracted from the materials project. Feature evaluation emphasizes that cathode properties depend on the crystal structure, and optimized classification strategies lead to better predictability. Ensemble machine learning algorithms such as Random Forest, Extremely Randomized Trees, and Gradient Boosting Machines have demonstrated the best predictive capabilities for crystal systems under the Monte Carlo cross-validation test. Additionally, sequential forward selection (SFS) is performed to identify the most critical features influencing the prediction accuracy for different machine learning models, with Volume, Band gap, and Sites as input features ensemble machine learning algorithms such as Random Forest (80.69\%), Extremely Randomized Tree(78.96\%), and Gradient Boosting(80.69\%) provide the maximum accuracy.
% % \end{abstract}

% % \begin{IEEEkeywords}
% % Crystal system, Ensemble machine learning, Monte Carlo cross-validation, sequential forward selection
% % \end{IEEEkeywords}
% % \section{Introduction}
% % Lithium-ion batteries (LiBs) play a pivotal role in modern energy storage technologies, powering various applications, from portable electronics to electric vehicles\cite{ahsan2022} and grid-scale energy storage\cite{chen2020}. The performance, efficiency, and longevity of LiBs are significantly influenced by the properties of their cathode materials, which are often governed by their crystal structure \cite{chen2024}. Understanding and predicting the crystal system of cathode materials is crucial because the arrangement of atoms in a crystal lattice directly affects key characteristics of the material, such as ion diffusion pathways\cite{zhang2013}, electrical conductivity\cite{cai2021} and stability under operating conditions\cite{de2019}. The selection and optimization of cathode materials have traditionally relied on experimental techniques, such as X-ray diffraction (XRD) \cite{duncan2011}, and computational methods, such as density functional theory (DFT)\cite{kalantarian2013}, to determine their crystal systems. Although these methods provide accurate results, they are often resource-intensive and time-consuming. 
% % Machine learning (ML) provides a data-driven approach to identifying patterns/ trends and making predictions, enabling researchers to address complex problems that are challenging to solve through conventional methods\cite{mobarak2023scope}. For LiB cathode materials, ML has proven to be a promising tool in predicting critical materials' properties, including the crystal system, based on input features such as chemical composition and thermodynamic properties\cite{shandiz2016application}.  Today, researchers have access to a huge amount of information about the predicted properties of materials. The schematics of the machine learning framework is explained in Fig.\ref{fig:workflow} explaining the three components: (i) data processing, (ii) model training, and (iii) feature selections. For example, the Materials Project provides a free, web-based platform where anyone can explore the physical and chemical properties of both known and predicted materials. These properties are calculated using DFT, which helps in estimating physical properties such as crystallographic structure and electronic bandgap of materials\cite{jain2011high,erum2017mechanical,jain2013jain}. Improvements in exchange-correlation potentials have made it possible to accurately calculate the physical properties of many different materials, including those used in lithium-ion batteries\cite{yan2014review,zhao2022first,ceder1997application,ong2011first}.
% % Phosphate-based cathode materials with Li–P–(V, Mn, Fe, Co, Ni)–O compositions are of great interest for research due to their high capacity (volumetric and gravimetric) and stability \cite{hautier2011phosphates,padhi1997phospho}.This study utilizes various classification algorithms to predict the crystal systems (Monoclinic, Triclinic, and Orthorhombic) of cathode materials with Li–P–(V, Mn, Fe, Co, Ni)–O compositions based on data obtained from the Materials Project. Machine learning models, including Linear Discriminant Analysis (LDA), Support Vector Machines (SVM), K-Nearest Neighbors (KNN), and ensemble methods such as Random Forest (RF), Extremely Randomized Trees (ERT), and Gradient Boosting Machines (GBM), were employed for this task. Monte Carlo Cross-Validation (MCCV) was conducted to ensure reliable performance evaluation. A sequential forward selection (SFS) approach was employed to enhance model interpretability and performance and identify each machine learning model's the best three most influential features. These key features were used to construct feature subsets, and models were retrained and evaluated with different feature combinations. This approach identified optimal feature sets for achieving high prediction accuracy and provided valuable insight into the role of specific cathode properties in influencing the crystal system. 
% % \begin{figure}[!ht]
% %     \centering    \includegraphics[width=\linewidth]{flowchart for model.png } 
% %     \caption{Machine learning Framework for Data Processing, Model Training, and Feature Selection}
% %     \label{fig:workflow} 
% % \end{figure}
% % \section{Methodology }
% % \subsection{The dataset}
% % The dataset consists of results from DFT calculations for one thousand eight hundred nineteen (1819) cathode materials with  Li–P–(V, Mn, Fe, Co, Ni)–O compositions derived from the Materials Project.The Materials Project generates data using advanced computational tools and methods, leveraging first-principles density functional theory (DFT) framework using a generalized gradient approximation (GGA) functional parametrized by Perdew Burke and Ernzerhof (PBE)\cite{jain2013commentary,perdew1996generalized}.  The transition metals, Fe,Co, Mn, Ni, and V have been assigned a U parameter to correct for the self-interaction part in GGA\cite{anisimov1991band,anisimov1997first}.
% % The DFT calculations and optimizations in the Materials Project are carried out using VASP software \cite{kresse1996efficiency}.The selected materials are symbolically presented in Table.\ref{tab:materials} with respective materials parameters used in the present work. The data set includes attributes such as chemical formula, space group symbol, space group number, formation energy $(E_f)$, energy above hull $(E_H)$, bandgap $(E_g)$, number of atomic sites $(N_s)$, density $(\rho)$, unit cell volume (V) and crystal system.
% % The parameters $N_s$ and $\rho$ represent the number of atoms in a unit cell and the density of the bulk crystalline material, respectively. Machine learning models in this context often use V=M/$\rho$
% %  (where M is atomic mass) as a primary variable. $E_H$ describes the energy required for the decomposition into the most stable phases\cite{jain2013jain}. All properties in the data set are calculated at 0 K and ambient pressure, with $E_g$ and V potentially influenced by temperature and pressure. However, these factors are held constant for this analysis. Fig.\ref{fig:bar_plt} shows the number of different crystallographic systems, i.e., monoclinic (978), triclinic (564), and orthorhombic (277) crystal systems in the present data set. Formation energy, energy above the hull, bandgap, sites, and volume were used as input features in the machine learning model with the crystal system serving as the output feature.
% % \begin{table*}[ht]
% % \centering
% % \caption{Dataset of selected phosphate materials with properties used in the study.}
% % \setlength{\tabcolsep}{4pt} % Adjust column spacing
% % \renewcommand{\arraystretch}{1.2} % Adjust row spacing
% % \begin{adjustbox}{max width=\textwidth}
% % \begin{tabular}{@{}l l r r r r r r r l@{}}
% % \toprule
% % \textbf{Formula} & \textbf{Space Group Symbol} & \textbf{Space Group Number} & \textbf{Sites ($N_s$)} & \textbf{$E_H$ (eV)} & \textbf{$E_F$ (eV)} & \textbf{Volume ($\text{\AA}^3$)} & \textbf{Density ($\text{g/cm}^3$)} & \textbf{Band Gap (eV)} & \textbf{Crystal System} \\ 
% % \midrule
% % Li$_2$FeP$_2$O$_7$ & P-1 & 2 & 48 & 0.033236 & -2.539132 & 587.586162 & 2.754 & 4.3546 & Triclinic \\
% % Li$2$VP$_4$O${13}$ & P-1 & 2 & 40 & 0.045837 & -2.616142 & 537.320824 & 2.452 & 1.8286 & Triclinic \\
% % Li$_6$Ni$_5$(P$_2$O$_7$)$_4$ & P-1 & 2 & 94 & 0.038332 & -2.407053 & 1049.598321 & 3.261 & 3.4812 & Triclinic \\
% % Li$_2$V$_3$(PO$_4$)$_3$ & C2 & 5 & 40 & 0.059565 & -2.599565 & 474.363850 & 3.162 & 0.4490 & Monoclinic \\
% % Li$_3$Mn$_2$(PO$_3$)$_7$ & P-1 & 2 & 66 & 0.083130 & -2.558361 & 846.570725 & 2.681 & 4.0116 & Triclinic \\
% % \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots \\
% % LiCo(PO$_3$)$_3$ & P2$_1$2$_1$2$_1$ & 19 & 56 & 0.000464 & -2.498897 & 640.941708 & 3.138 & 2.7884 & Orthorhombic \\
% % \bottomrule
% % \end{tabular}
% % \end{adjustbox}
% % \label{tab:materials}
% % \end{table*}
% % \begin{figure}[!ht]
% %     \centering
% %     \includegraphics[width=\linewidth]{barplot for number of crystal system.png} 
% %     \caption{Bar plot showing the distribution of crystal systems}
% %     \label{fig:bar_plt} 
% % \end{figure}
% % \subsection{ Statistical Analysis}
% % A pair plot, Fig.\ref{fig:pair_plt}, is a valuable tool for visualizing the relationships between multiple features in a dataset. It generates a matrix of scatter plots, each representing the relationship between two features. The diagonal of the matrix typically shows the distribution (density plots) of individual features. In a classification problem, a pair plot shows how well the different classes are separated based on features. The box plots, Fig.\ref{fig:box_plt}(a-e), illustrate the distribution of key input features (Volume, Sites, Energy Above Hull, Formation Energy, and Band Gap) across three crystal systems: Monoclinic, Orthorhombic, and Triclinic. These plots provide valuable insights into the distribution of features within the different crystal systems, enabling the identification of trends, variability, and outliers crucial for feature analysis and model refinement. Monoclinic systems generally show the widest ranges and more outliers in properties like Band gap, Volume, and Formation Energy. Triclinic systems tend to have narrower ranges but more extreme outliers in Energy Above Hull. Orthorhombic systems exhibit intermediate distributions with fewer outliers. Overall, monoclinic systems exhibit more significant variability than the other two crystal systems.
% % The Heatmap Fig.\ref{fig:box_plt}(f)  provides the linear relationships between the numerical features in the data set. A strong negative correlation is noticed between formation energy and the band gap ( -0.15). This suggests that materials with low formation energy have a higher band gap. Positive correlations, such as between Sites and Volume (0.98), indicate that larger unit cells (more sites) tend to have higher volumes, and Weak correlations (-0.0059) imply negligible relationships between certain features, like Energy above hull and Volume. 
% % \begin{figure}[!ht]
% %     \centering
% %     \includegraphics[width=\linewidth]{pair.png} 
% %     \caption{ The pairs plot of different properties of Li– (Mn, Fe, Co, Ni, V)–P–O cathodes based on the extracted 
% % data from the Materials Project.}
% %     \label{fig:pair_plt} 
% % \end{figure}
% % \begin{figure}[!ht]
% % \includegraphics[width=\linewidth]{box1.png} 
% % \includegraphics[width=\linewidth]{box2.png}     \includegraphics[width=\linewidth]{box3.png}  
% %     \caption{(a-e)Box plot and (f) Correlation analysis is represented as a heatmap.}
% %     \label{fig:box_plt} 
% % \end{figure}

% % \subsection{ Outlier Detection and Removal} Outlier detection was performed to identify extreme values within the different features of the data set and the results are summarized in Fig.\ref{fig:IQR}. The Interquartile Range (IQR) method, a robust statistical approach, is used to effectively identify and remove data points, falling outside a reasonable range. The IQR is calculated as the difference between each feature's 75th percentile (Q3) and 25th percentile (Q1). Outliers were defined as values below (Q1 - 1.5 × IQR) or above (Q3 + 1.5 × IQR). The data set contained 1,819 data points. After applying the IQR-based removal process, approximately 4.84\% of the total data points were identified as outliers, reducing the dataset to 1,731 data points.
% % \begin{figure}[!ht]  
% %     \includegraphics[width=\linewidth]{IQR.png} 
% %     \caption{Outlier Detection and Elimination via Interquartile Range}
% %     \label{fig:IQR} 
% % \end{figure}
% % \subsection{ CLASSIFICATION TECHNIQUES}
% % \subsubsection{Linear Discriminant Analysis}
% % Linear Discriminant Analysis (LDA)Fig.\ref{fig:models}(a) is a supervised dimensionality reduction technique used for classification. It projects high-dimensional data onto a lower-dimensional space while maximizing class separability\cite{sharma2015linear}. LDA assumes that class distributions are Gaussian with a common covariance matrix\cite{xanthopoulos2013linear}. 

% % The method involves computing class means, within-class (\(S_W\)) and between-class (\(S_B\)) scatter matrices, and solving the eigenvalue problem:
% % \[
% % S_B \mathbf{w} = \lambda S_W \mathbf{w}
% % \]
% % to obtain optimal projection directions\cite{tharwat2017linear}. For a 3-class problem, data are reduced to 2 dimensions (\(k-1\)). The transformed data are classified using class centroids and distance metrics.

% % \subsubsection{Support Vector Machine (SVM)}
% % Support Vector Machine (SVM)Fig.\ref{fig:models}(b) is a supervised learning algorithm widely used for classification tasks due to its ability to find an optimal hyperplane that maximizes the margin between different classes \cite{jakkula2006tutorial}. It is particularly effective in handling high-dimensional data and can be extended to non-linearly separable cases using kernel functions \cite{yue2003svm}. 
% % In this study, the Radial Basis Function (RBF) kernel was used to capture complex decision boundaries. The regularization parameter \( C = 10 \) was chosen to control the trade-off between maximizing the margin and minimizing classification errors. 

% % \begin{figure}[!ht]
% % \includegraphics[width=\linewidth]{ldasvm.png} 
% %  \includegraphics[width=\linewidth]{knnrf.png}     \includegraphics[width=\linewidth]{ERT.png}  
% % \includegraphics[width=\linewidth]{GBM.png}
% % \caption{Schematic Representation of Machine Learning Models including (a) Linear Discriminant Analysis (LDA) for classification and dimensionality reduction, (b) Support Vector Machine (SVM) for optimal hyperplane-based separation, (c) k-Nearest Neighbors (K-NN) for instance-based classification, (d) Random Forest (RF) for ensemble-based decision-making, (e) Extremely Randomized Trees (Extra Trees) for enhanced randomness in tree construction, and (f) Gradient Boosting Machine (GBM) for iterative error minimization and improved predictive performance.}
% % \label{fig:models} 
% % \end{figure}
% % \subsubsection{k-Nearest Neighbors (kNN)}
% % K-Nearest Neighbors (kNN), Fig.\ref{fig:models}(c) is a simple, non-parametric classification algorithm that assigns a class label based on the majority class among its \( K \) nearest neighbors\cite{altman1992introduction}. The model's performance depends on the choice of \( K \) and the distance metric\cite{hechenbichler2004weighted}. 

% % For this study, we used \( K = 3 \), i.e., classification was based on the three closest neighbors. The Euclidean distance metric was used to measure proximity, and uniform weighting was applied to all neighbors. These hyperparameter choices ensured a balance between model complexity and classification accuracy.

% % \subsubsection{Random Forest}
% % Random forest (RF), Fig.\ref{fig:models}(d), is a popular and powerful ensemble-supervised classification method\cite{breiman2001random}.
% % Random Forest (RF) is an ensemble classification algorithm that constructs decision trees using random subsets of data and features (bagging and random subspaces). Random selection of features at each split reduces tree correlation, improving prediction power and efficiency. RF overcomes the overfitting problem and is less sensitive to outliers, and thus, it does not require pruning. It handles continuous categorical data, manages missing values, and provides automatic measures of variable importance and accuracy. Combining bootstrapping, ensemble strategies, and random sampling ensures high prediction accuracy, generalizability, and interpretability, making RF suitable for high-dimensional data sets\cite{ali2012random}. In this study, we used 100 estimators, a maximum tree depth of 10 to avoid overfitting, and a random state of 40 to ensure reproducibility.
% % \subsubsection{Extremely Randomized Trees} Extremely Randomized Trees (ERT), Fig.\ref{fig:models}(e), is an ensemble learning method designed for classification and regression tasks. It enhances the Random Forest approach by introducing additional randomness during the construction of decision trees. Unlike traditional methods, ERT selects both the attributes and the splitting thresholds at random, rather than optimizing them. This approach creates greater diversity among the trees in the ensemble, improving generalization and reducing the risk of overfitting. The algorithm builds multiple unpruned decision trees, and predictions are made by averaging in regression tasks or through majority voting in classification problems. Due to its randomized splitting criterion, ERT is computationally efficient and achieves competitive accuracy, making it well-suited for high-dimensional data sets\cite{geurts2006extremely}.In this study, the model was configured with 150 estimators, a maximum tree depth of 20, and a random state of 40.

% % \subsubsection{Gradient Boosting Machine (GBM)}
% % Gradient Boosting, Fig.\ref{fig:models}(f), is an ensemble learning technique that builds a strong predictive model by sequentially combining multiple weak models, typically decision trees\cite{natekin2013gradient}. It works by iteratively minimizing residual errors, where each new tree corrects the mistakes of the previous ones\cite{ayyadevara2018gradient}. The algorithm follows these key steps:

% % \begin{enumerate}
% %     \item A base model (typically a decision tree) is initialized to make initial predictions.
% %     \item The residuals (difference between actual and predicted values) are computed.
% %     \item A new decision tree is trained to predict these residuals.
% %     \item The model is updated by adjusting predictions with a scaled contribution from the new tree, controlled by the learning rate.
% %     \item This process is repeated for a predefined number of iterations to refine the predictions.
% % \end{enumerate}
% % For this study, the best hyperparameters were: \text{Number of estimators} = 200, \text{Learning rate} = 0.1, and \text{Maximum depth} = 5, ensuring optimal model performance.

% % \section{ Result \& Discussion}
% % We used Python and the required Python libraries to build machine learning models and calculate their performance.
% % The data set was divided into 80\% for training and 20\% for testing purposes. The 80\% training data was utilized to train multiple machine learning models, including LDA, SVM, KNN, RF, ERT, and GBM, followed by hyperparameter tuning. Among these models, the GBM,ERT, and RF models achieved the highest test accuracy: 74.06\%,74.93\%, and 76.37\%, respectively, on the 20\% test data because GBM, ERT, and RF are ensemble models; they outperform simpler models like LDA by capturing nonlinear relationships and feature interactions in the data. Unlike LDA, which assumes linear boundaries and Gaussian distributions, these models make no such assumptions and are robust to noise and outliers. Their ability to aggregate predictions from multiple decision trees improves generalization and accuracy, making them particularly effective for complex and high-dimensional data sets.
% % Fig.\ref{fig:confusion_matrix} Summarizes the performance of different machine learning models by classifying data into three classes (0, 1, and 2) using confusion matrices. Here, the labels 0, 1, and 2 represent the monoclinic, triclinic, and orthorhombic crystal systems. These matrices illustrate the alignment between the predicted and actual class labels, providing a detailed classification accuracy assessment for each class.
% % \begin{figure}[!ht]
% %     \centering    \includegraphics[width=\linewidth]{confusion.png} 
% %     \caption{Confusion Matrix for the different models where 0,1 and 2 correspond to monoclinic, triclinic, and      
% % Orthorhombic crystal systems, respectively.}
% %     \label{fig:confusion_matrix} 
% % \end{figure}
% % Monte Carlo Cross-Validation (MCCV), also known as repeated random sub-sampling, was utilized to assess model accuracy and detect overfitting. This method involves repeatedly splitting the dataset into training and randomly testing subsets. For each split, the model was trained on the training set and tested on the testing set, and the prediction accuracy was recorded. The process was repeated 100 times to ensure reliable and consistent results.
% % The dataset was shuffled before each split using 100 predefined random seeds to standardize the evaluation, ensuring all classifiers were tested on identical splits for direct performance comparison.
% % The Fig.\ref{fig:mccv model}shows the relationship between the percentage of training data and mean accuracy for six machine learning models using MCCV. GBM and ERT consistently achieve the highest accuracy, while Linear Discriminant Analysis (LDA) performs the least among all. Increasing the training percentage improves the accuracy for all models, but the gains diminish as it approaches 90\%. Ensemble models like GBM and ERT exhibit robust performance and generalization compared to others, highlighting their effectiveness for the dataset.Fig.\ref{fig:std} The plot shows that Extra Trees (ERT) and Gradient Boosting (GBM) exhibit the lowest standard deviation, indicating the most consistent and stable performance across different Monte Carlo Cross-Validation (MCCV) splits. This reflects their robustness to variations in train-test splits and their ability to generalize well to unseen data. Their ensemble-based mechanisms effectively minimize variability, making them reliable models for this task. Lower std for these models implies higher dependability in predictive performance.
% % \begin{figure}[!ht]
% %     \centering
% %     \includegraphics[width=\linewidth]{MCCV.png} 
% %     \caption{Effect of percentage of training data for building ML models on the mean accuracy. }
% %     \label{fig:mccv model} 
% % \end{figure}
% % \begin{figure}[!ht]
% %     \centering
% %     \includegraphics[width=\linewidth]{Std.png} \caption{Curve between training data percentage Vs standard deviation in MCCV}
% %     \label{fig:std} 
% % \end{figure}
% % \begin{figure}[!ht]
% %     \centering
% %     \includegraphics[width=\linewidth]{sequential fs.png} 
% %     \caption{Schematic representation of forward sequential feature selection}
% %     \label{fig:sfs model} 
% % \end{figure}
% % Following Monte Carlo Cross-Validation, Fig.\ref{fig:sfs model} Sequential Forward Selection (SFS) enhanced model performance and interpretability by identifying the most significant input features. This iterative process starts with no features and adds the feature that improves model accuracy the most, continuing until a predefined number of features (three in this case) are selected. The models were re-trained and evaluated using the selected features, reducing dimensionality, and focusing on the most relevant properties. SFS highlighted the importance of features, such as volume, band gap, and atomic sites in improving classification accuracy across all models.
% % Once the top three features were identified, the classifier was trained using 80\% of the data with only these selected features. The model's accuracy was then evaluated on the remaining 20\% of the data. This approach reduces dimensionality, focuses on the most relevant features, and enhances the classifier's overall performance.

% % \begin{figure}[!ht]
% %     \centering
% %     \includegraphics[width=\linewidth]{sfs important features.png} 
% %     \caption{Top Features and Their Importance for Best Performing Models}
% %     \label{fig: feature importance} 
% % \end{figure}

% % Fig.\ref{fig: feature importance}
% % illustrates the performance of the best ensemble machine learning models, highlighting their top three input features based on importance, along with the corresponding accuracy achieved using these features.

% % \begin{table}[h!]
% % \centering
% % \caption{ Machine learning models with their selected features and corresponding accuracy.}
% % \begin{tabular}{lcccccc}
% % \toprule
% % \textbf{Model} & \(\mathbf{E_f}\) & \(\mathbf{E_H}\) & \textbf{Volume} & \textbf{Band Gap} & \textbf{Sites} & \textbf{Accuracy (\%)} \\
% % \midrule
% % LDA &  & \checkmark &  \checkmark & &\checkmark & 56.48 \\
% % SVM &  & \checkmark &  &  \checkmark & \checkmark & 66.28 \\
% % KNN & \checkmark &  & \checkmark &  & \checkmark & 70.32 \\
% % RF  & \checkmark &  &  \checkmark & & \checkmark & 78.67 \\
% % ERT &  &  & \checkmark & \checkmark & \checkmark & 78.96 \\
% % GBM &  &  & \checkmark & \checkmark & \checkmark & 80.40\\
% % \bottomrule
% % \end{tabular}
% % \label{tab:model_features}
% % \end{table}
% % Table\ref{tab:model_features} summarizes the results of Sequential Feature Selection applied to six machine learning models. The table identifies the top three features for each model, including Formation Energy \(E_f\), Energy Above Hull \(E_H\), Volume, Band Gap, and Sites, along with their corresponding accuracies. GBM achieved the highest accuracy (80.40\%) using Volume, Band gap, and Sites, while Sites were consistently selected across all models. These results demonstrate the effectiveness of SFS in reducing dimensionality and improving model performance by focusing on the most relevant features.

% % \begin{table}[h!]
% % \centering
% % \caption{Accuracy (\%) of Machine Learning Models with Different Input Features.}
% % \label{tab:ml_models_accuracy}
% % \begin{tabular}{lcccccc}
% % \toprule
% % \textbf{Features} & \textbf{LDA} & \textbf{SVM} & \textbf{KNN} & \textbf{RF} & \textbf{ERT} & \textbf{GBM} \\
% % \midrule
% % E\(_H\), Band Gap, Sites & 57.06 & 66.28 & 68.88 & 74.93 & 74.06 & 72.91 \\
% % E\(_f\), Sites, Volume    & 56.48 & 68.30 & 73.49 & 78.67 & 77.52 & 79.54 \\
% % E\(_H\), Sites, Volume    & 56.48 & 71.47 & 71.47 & 76.95 & 77.81 & 80.40 \\
% % Band Gap, Sites, Volume   & 56.48 & 64.84 & 75.50 & 80.69 & 78.96 & 80.40 \\
% % \bottomrule
% % \end{tabular}
% % \end{table}
% % The table.\ref{tab:ml_models_accuracy} represents the results of training six machine learning models (LDA, SVM, KNN, RF, ERT, GBM) using different input feature combinations identified through Sequential Feature Selection (SFS). Each row represents a unique combination of features—such as Energy Above Hull \(E_H\), Formation Energy \(E_f\), Volume, Band gap, and Sites—and their corresponding accuracies. Models like GBM and RF consistently achieved high accuracy across combinations, showcasing their robustness, while LDA performed poorly, as can be inferred from Fig. \ref{fig:con_matrix}. The combination of Band gap, Sites, and Volume as input features yielded the best overall performance, with RF achieving 80.69\% and GBM 80.40\% test accuracy. This highlights the importance of feature selection in enhancing model performance.
% % \begin{figure}[!ht]
% %     \centering
% %     \includegraphics[width=\linewidth]{confusion matrix with best features.png}   
% %     \caption{The performance matrix of different models with Band Gap, Sites, and Volume as input features. }
% %     \label{fig:con_matrix} 
% % \end{figure}
% % Further, we assessed the thermodynamic stability of cathode materials using a filtering process, focusing on energy above hull and formation energy as stability indicators. Materials were filtered based on two criteria: energy above hull $\leq$ 0.014 eV and formation energy $<$ -2.672 eV. The analysis revealed that vanadium-based materials, particularly $\mathrm{Li_3V_2(PO_4)_3}$ in the monoclinic phase, exhibited the highest stability, with a formation energy of -2.725 eV and energy above hull of 0 eV. These results suggest $\mathrm{Li_3V_2(PO_4)_3}$ as a promising candidate for lithium-ion battery cathodes.
% % \begin{figure}[!ht]
% %     \centering
% %     \includegraphics[width=\linewidth]{stable_material.png}   
% %     \caption{The number of V based stable cathode materials along with their respective crystal systems. }
% %     \label{fig: stable material } 
% % \end{figure}
% %  \section{Conclusion}
% % This study demonstrated the effectiveness of machine learning models in classifying Li–P–(V, Mn, Fe, Co, Ni)–O-based cathode materials into three crystal systems (monoclinic, triclinic, and orthorhombic). Sequential Feature Selection (SFS) and Monte Carlo Cross-Validation (MCCV) are used as the key components in optimizing model performance and ensuring robust evaluation. The ensemble models like GBM, ERT, and RF achieved the highest accuracy of around 80.40\%, 78.96\%, and 80.69\%, respectively, using the top three selected features: Volume, Band gap, and Sites. This study highlights the power of ensemble methods and optimized feature selection for achieving reliable and robust predictions in crystal system classification tasks. Apart
% %  from this, the V-based phosphate-based materials are more stable than others.

% % \bibliographystyle{IEEEtran}
% % \bibliography{refernces}
% % \vspace{12pt}
% % \end{document}





















% \documentclass[12pt, onecolumn]{article}
% \usepackage{cite}
% \usepackage{booktabs}
% \usepackage{adjustbox}
% \usepackage{hyperref}
% \usepackage{amsmath,amssymb,amsfonts}
% \usepackage{algorithmic}
% \usepackage{graphicx}
% \usepackage{textcomp}
% \usepackage{xcolor}
% \usepackage[margin=1in]{geometry}
% \usepackage{array}
% \usepackage{caption}
% \usepackage{subcaption}

% \title{Crystal Systems Classification Using Machine Learning\\ for Phosphate Based Cathode Materials in Lithium-Ion Battery}

% \author{
% Yogesh Yadav\textsuperscript{1}, Dr. Sandeep K Yadav\textsuperscript{2}, \\
% Dr. Vivek Vijay\textsuperscript{3}, Dr. Ambesh Dixit\textsuperscript{4} \\
% \textsuperscript{1}Department of Physics, Indian Institute of Technology Jodhpur \\
% \textsuperscript{2}Department of Electrical Engineering, Indian Institute of Technology Jodhpur \\
% \textsuperscript{3}Department of Mathematics, Indian Institute of Technology Jodhpur \\
% \textsuperscript{4}Department of Physics, Indian Institute of Technology Jodhpur \\
% }

% \begin{document}

% \maketitle

% \begin{abstract}
% The physical and chemical characteristics of cathodes used in batteries are derived from the lithium-ion phosphate cathodes' crystalline arrangement, which is pivotal to the overall battery performance. Therefore, the correct prediction of the crystal system is essential to estimate the properties of cathodes. This study investigates machine learning classification algorithms for predicting the crystal systems, namely monoclinic, orthorhombic, and triclinic, related to Li-P-(Mn, Fe, Co, Ni, V)-O based Phosphate cathodes. The data used in this work is extracted from the materials project. Feature evaluation emphasizes that cathode properties depend on the crystal structure, and optimized classification strategies lead to better predictability. Ensemble machine learning algorithms such as Random Forest, Extremely Randomized Trees, and Gradient Boosting Machines have demonstrated the best predictive capabilities for crystal systems under the Monte Carlo cross-validation test. Additionally, sequential forward selection (SFS) is performed to identify the most critical features influencing the prediction accuracy for different machine learning models, with Volume, Band gap, and Sites as input features ensemble machine learning algorithms such as Random Forest (80.69\%), Extremely Randomized Tree(78.96\%), and Gradient Boosting(80.69\%) provide the maximum accuracy.
% \end{abstract}

% \textbf{Keywords:} Crystal system, Ensemble machine learning, Monte Carlo cross-validation, sequential forward selection

% \section{Introduction}
% Lithium-ion batteries (LiBs) play a pivotal role in modern energy storage technologies, powering various applications, from portable electronics to electric vehicles \cite{ahsan2022} and grid-scale energy storage \cite{chen2020}. The performance, efficiency, and longevity of LiBs are significantly influenced by the properties of their cathode materials, which are often governed by their crystal structure \cite{chen2024}. 

% \begin{figure}[h]
% \centering
% \includegraphics[width=0.8\textwidth]{flowchart_for_model.png}
% \caption{Machine learning Framework for Data Processing, Model Training, and Feature Selection}
% \label{fig:workflow}
% \end{figure}

% Understanding and predicting the crystal system of cathode materials is crucial because the arrangement of atoms in a crystal lattice directly affects key characteristics of the material, such as ion diffusion pathways \cite{zhang2013}, electrical conductivity \cite{cai2021} and stability under operating conditions \cite{de2019}. The selection and optimization of cathode materials have traditionally relied on experimental techniques, such as X-ray diffraction (XRD) \cite{duncan2011}, and computational methods, such as density functional theory (DFT) \cite{kalantarian2013}, to determine their crystal systems. Although these methods provide accurate results, they are often resource-intensive and time-consuming. 

% Machine learning (ML) provides a data-driven approach to identifying patterns/trends and making predictions, enabling researchers to address complex problems that are challenging to solve through conventional methods \cite{mobarak2023scope}. For LiB cathode materials, ML has proven to be a promising tool in predicting critical materials' properties, including the crystal system, based on input features such as chemical composition and thermodynamic properties \cite{shandiz2016application}.  

% Today, researchers have access to a huge amount of information about the predicted properties of materials. The schematics of the machine learning framework is explained in Fig. \ref{fig:workflow} explaining the three components: (i) data processing, (ii) model training, and (iii) feature selections. For example, the Materials Project provides a free, web-based platform where anyone can explore the physical and chemical properties of both known and predicted materials. These properties are calculated using DFT, which helps in estimating physical properties such as crystallographic structure and electronic bandgap of materials \cite{jain2011high,erum2017mechanical,jain2013jain}. Improvements in exchange-correlation potentials have made it possible to accurately calculate the physical properties of many different materials, including those used in lithium-ion batteries \cite{yan2014review,zhao2022first,ceder1997application,ong2011first}.

% Phosphate-based cathode materials with Li-P-(V, Mn, Fe, Co, Ni)-O compositions are of great interest for research due to their high capacity (volumetric and gravimetric) and stability \cite{hautier2011phosphates,padhi1997phospho}. This study utilizes various classification algorithms to predict the crystal systems (Monoclinic, Triclinic, and Orthorhombic) of cathode materials with Li-P-(V, Mn, Fe, Co, Ni)-O compositions based on data obtained from the Materials Project. 

% \section{Methodology}
% \subsection{The dataset}
% The dataset consists of results from DFT calculations for 1,819 cathode materials with Li-P-(V, Mn, Fe, Co, Ni)-O compositions derived from the Materials Project. The Materials Project generates data using advanced computational tools and methods, leveraging first-principles density functional theory (DFT) framework using a generalized gradient approximation (GGA) functional parametrized by Perdew Burke and Ernzerhof (PBE) \cite{jain2013commentary,perdew1996generalized}. The transition metals, Fe, Co, Mn, Ni, and V have been assigned a U parameter to correct for the self-interaction part in GGA \cite{anisimov1991band,anisimov1997first}.

% \begin{table}[h]
% \centering
% \caption{Dataset of selected phosphate materials with properties used in the study.}
% \begin{tabular}{@{}lccrrrrrl@{}}
% \toprule
% Formula & Space Group & SG No. & Sites & $E_H$ (eV) & $E_F$ (eV) & Volume (\AA$^3$) & Density (g/cm$^3$) & Band Gap (eV) & Crystal System \\ 
% \midrule
% Li$_2$FeP$_2$O$_7$ & P-1 & 2 & 48 & 0.033 & -2.539 & 587.59 & 2.754 & 4.3546 & Triclinic \\
% Li$2$VP$_4$O${13}$ & P-1 & 2 & 40 & 0.046 & -2.616 & 537.32 & 2.452 & 1.8286 & Triclinic \\
% Li$_6$Ni$_5$(P$_2$O$_7$)$_4$ & P-1 & 2 & 94 & 0.038 & -2.407 & 1049.60 & 3.261 & 3.4812 & Triclinic \\
% \bottomrule
% \end{tabular}
% \label{tab:materials}
% \end{table}

% The DFT calculations and optimizations in the Materials Project are carried out using VASP software \cite{kresse1996efficiency}. The selected materials are symbolically presented in Table \ref{tab:materials} with respective materials parameters used in the present work. The data set includes attributes such as chemical formula, space group symbol, space group number, formation energy ($E_f$), energy above hull ($E_H$), bandgap ($E_g$), number of atomic sites ($N_s$), density ($\rho$), unit cell volume (V) and crystal system.

% \begin{figure}[h]
% \centering
% \includegraphics[width=0.7\textwidth]{barplot_for_number_of_crystal_system.png}
% \caption{Bar plot showing the distribution of crystal systems}
% \label{fig:bar_plt}
% \end{figure}

% The parameters $N_s$ and $\rho$ represent the number of atoms in a unit cell and the density of the bulk crystalline material, respectively. Machine learning models in this context often use V = M/$\rho$ (where M is atomic mass) as a primary variable. $E_H$ describes the energy required for the decomposition into the most stable phases \cite{jain2013jain}. All properties in the data set are calculated at 0 K and ambient pressure, with $E_g$ and V potentially influenced by temperature and pressure. However, these factors are held constant for this analysis. Fig. \ref{fig:bar_plt} shows the number of different crystallographic systems, i.e., monoclinic (978), triclinic (564), and orthorhombic (277) crystal systems in the present data set. Formation energy, energy above the hull, bandgap, sites, and volume were used as input features in the machine learning model with the crystal system serving as the output feature.

% \subsection{Statistical Analysis}
% A pair plot, Fig. \ref{fig:pair_plt}, is a valuable tool for visualizing the relationships between multiple features in a dataset. It generates a matrix of scatter plots, each representing the relationship between two features. The diagonal of the matrix typically shows the distribution (density plots) of individual features. In a classification problem, a pair plot shows how well the different classes are separated based on features. 

% \begin{figure}[h]
% \centering
% \includegraphics[width=0.8\textwidth]{pair.png}
% \caption{The pairs plot of different properties of Li-(Mn, Fe, Co, Ni, V)-P-O cathodes based on the extracted data from the Materials Project.}
% \label{fig:pair_plt}
% \end{figure}

% The box plots, Fig. \ref{fig:box_plt}(a-e), illustrate the distribution of key input features (Volume, Sites, Energy Above Hull, Formation Energy, and Band Gap) across three crystal systems: Monoclinic, Orthorhombic, and Triclinic. These plots provide valuable insights into the distribution of features within the different crystal systems, enabling the identification of trends, variability, and outliers crucial for feature analysis and model refinement. Monoclinic systems generally show the widest ranges and more outliers in properties like Band gap, Volume, and Formation Energy. Triclinic systems tend to have narrower ranges but more extreme outliers in Energy Above Hull. Orthorhombic systems exhibit intermediate distributions with fewer outliers. Overall, monoclinic systems exhibit more significant variability than the other two crystal systems.

% \begin{figure}[h]
% \centering
% \includegraphics[width=0.8\textwidth]{box1.png}
% \includegraphics[width=0.8\textwidth]{box2.png}    
% \includegraphics[width=0.8\textwidth]{box3.png}
% \caption{(a-e) Box plot and (f) Correlation analysis represented as a heatmap.}
% \label{fig:box_plt}
% \end{figure}

% The Heatmap Fig. \ref{fig:box_plt}(f) provides the linear relationships between the numerical features in the data set. A strong negative correlation is noticed between formation energy and the band gap (-0.15). This suggests that materials with low formation energy have a higher band gap. Positive correlations, such as between Sites and Volume (0.98), indicate that larger unit cells (more sites) tend to have higher volumes, and Weak correlations (-0.0059) imply negligible relationships between certain features, like Energy above hull and Volume. 

% \subsection{Outlier Detection and Removal}
% Outlier detection was performed to identify extreme values within the different features of the data set and the results are summarized in Fig. \ref{fig:IQR}. The Interquartile Range (IQR) method, a robust statistical approach, is used to effectively identify and remove data points falling outside a reasonable range. The IQR is calculated as the difference between each feature's 75th percentile (Q3) and 25th percentile (Q1). Outliers were defined as values below (Q1 - 1.5 × IQR) or above (Q3 + 1.5 × IQR). The data set contained 1,819 data points. After applying the IQR-based removal process, approximately 4.84\% of the total data points were identified as outliers, reducing the dataset to 1,731 data points.

% \begin{figure}[h]
% \centering
% \includegraphics[width=0.7\textwidth]{IQR.png}
% \caption{Outlier Detection and Elimination via Interquartile Range}
% \label{fig:IQR}
% \end{figure}

% [... Additional sections continue in the same format ...]

% \bibliographystyle{IEEEtran}
% \bibliography{references}

% \end{document}





\documentclass[12pt, onecolumn]{article}
\usepackage{cite}
\usepackage{booktabs}
\usepackage{adjustbox}
\usepackage{hyperref}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{array}
\usepackage{caption}
\usepackage{subcaption}

\title{Crystal Systems Classification Using Machine Learning\\ for Phosphate Based Cathode Materials in Lithium-Ion Battery}

\author{
Yogesh Yadav\textsuperscript{1}, Dr. Sandeep K Yadav\textsuperscript{2}, \\
Dr. Vivek Vijay\textsuperscript{3}, Dr. Ambesh Dixit\textsuperscript{4} \\
\textsuperscript{1}Department of Physics, Indian Institute of Technology Jodhpur \\
\textsuperscript{2}Department of Electrical Engineering, Indian Institute of Technology Jodhpur \\
\textsuperscript{3}Department of Mathematics, Indian Institute of Technology Jodhpur \\
\textsuperscript{4}Department of Physics, Indian Institute of Technology Jodhpur \\
}

\begin{document}

\maketitle

\begin{abstract}
The physical and chemical characteristics of cathodes used in batteries are derived from the lithium-ion phosphate cathodes' crystalline arrangement, which is pivotal to the overall battery performance. Therefore, the correct prediction of the crystal system is essential to estimate the properties of cathodes. This study investigates machine learning classification algorithms for predicting the crystal systems, namely monoclinic, orthorhombic, and triclinic, related to Li-P-(Mn, Fe, Co, Ni, V)-O based Phosphate cathodes. The data used in this work is extracted from the materials project. Feature evaluation emphasizes that cathode properties depend on the crystal structure, and optimized classification strategies lead to better predictability. Ensemble machine learning algorithms such as Random Forest, Extremely Randomized Trees, and Gradient Boosting Machines have demonstrated the best predictive capabilities for crystal systems under the Monte Carlo cross-validation test. Additionally, sequential forward selection (SFS) is performed to identify the most critical features influencing the prediction accuracy for different machine learning models, with Volume, Band gap, and Sites as input features ensemble machine learning algorithms such as Random Forest (80.69\%), Extremely Randomized Tree(78.96\%), and Gradient Boosting(80.69\%) provide the maximum accuracy.
\end{abstract}

\textbf{Keywords:} Crystal system, Ensemble machine learning, Monte Carlo cross-validation, sequential forward selection

\section{Introduction}
Lithium-ion batteries (LiBs) play a pivotal role in modern energy storage technologies, powering various applications, from portable electronics to electric vehicles \cite{ahsan2022} and grid-scale energy storage \cite{chen2020}. The performance, efficiency, and longevity of LiBs are significantly influenced by the properties of their cathode materials, which are often governed by their crystal structure \cite{chen2024}. 

\begin{figure}[h]
\centering
\includegraphics[width=0.8\textwidth]{flowchart_for_model.png}
\caption{Machine learning Framework for Data Processing, Model Training, and Feature Selection}
\label{fig:workflow}
\end{figure}

Understanding and predicting the crystal system of cathode materials is crucial because the arrangement of atoms in a crystal lattice directly affects key characteristics of the material, such as ion diffusion pathways \cite{zhang2013}, electrical conductivity \cite{cai2021} and stability under operating conditions \cite{de2019}. The selection and optimization of cathode materials have traditionally relied on experimental techniques, such as X-ray diffraction (XRD) \cite{duncan2011}, and computational methods, such as density functional theory (DFT) \cite{kalantarian2013}, to determine their crystal systems. Although these methods provide accurate results, they are often resource-intensive and time-consuming. 

Machine learning (ML) provides a data-driven approach to identifying patterns/trends and making predictions, enabling researchers to address complex problems that are challenging to solve through conventional methods \cite{mobarak2023scope}. For LiB cathode materials, ML has proven to be a promising tool in predicting critical materials' properties, including the crystal system, based on input features such as chemical composition and thermodynamic properties \cite{shandiz2016application}.  

Today, researchers have access to a huge amount of information about the predicted properties of materials. The schematics of the machine learning framework is explained in Fig. \ref{fig:workflow} explaining the three components: (i) data processing, (ii) model training, and (iii) feature selections. For example, the Materials Project provides a free, web-based platform where anyone can explore the physical and chemical properties of both known and predicted materials. These properties are calculated using DFT, which helps in estimating physical properties such as crystallographic structure and electronic bandgap of materials \cite{jain2011high,erum2017mechanical,jain2013jain}. Improvements in exchange-correlation potentials have made it possible to accurately calculate the physical properties of many different materials, including those used in lithium-ion batteries \cite{yan2014review,zhao2022first,ceder1997application,ong2011first}.

Phosphate-based cathode materials with Li-P-(V, Mn, Fe, Co, Ni)-O compositions are of great interest for research due to their high capacity (volumetric and gravimetric) and stability \cite{hautier2011phosphates,padhi1997phospho}. This study utilizes various classification algorithms to predict the crystal systems (Monoclinic, Triclinic, and Orthorhombic) of cathode materials with Li-P-(V, Mn, Fe, Co, Ni)-O compositions based on data obtained from the Materials Project. 

\section{Methodology}
\subsection{The dataset}
The dataset consists of results from DFT calculations for 1,819 cathode materials with Li-P-(V, Mn, Fe, Co, Ni)-O compositions derived from the Materials Project. The Materials Project generates data using advanced computational tools and methods, leveraging first-principles density functional theory (DFT) framework using a generalized gradient approximation (GGA) functional parametrized by Perdew Burke and Ernzerhof (PBE) \cite{jain2013commentary,perdew1996generalized}. The transition metals, Fe, Co, Mn, Ni, and V have been assigned a U parameter to correct for the self-interaction part in GGA \cite{anisimov1991band,anisimov1997first}.

\begin{table}[h]
\centering
\caption{Dataset of selected phosphate materials with properties used in the study.}
\begin{tabular}{@{}lccrrrrrl@{}}
\toprule
Formula & Space Group & SG No. & Sites & $E_H$ (eV) & $E_F$ (eV) & Volume (\AA$^3$) & Density (g/cm$^3$) & Band Gap (eV) & Crystal System \\ 
\midrule
Li$_2$FeP$_2$O$_7$ & P-1 & 2 & 48 & 0.033 & -2.539 & 587.59 & 2.754 & 4.3546 & Triclinic \\
Li$2$VP$_4$O${13}$ & P-1 & 2 & 40 & 0.046 & -2.616 & 537.32 & 2.452 & 1.8286 & Triclinic \\
Li$_6$Ni$_5$(P$_2$O$_7$)$_4$ & P-1 & 2 & 94 & 0.038 & -2.407 & 1049.60 & 3.261 & 3.4812 & Triclinic \\
\bottomrule
\end{tabular}
\label{tab:materials}
\end{table}

The DFT calculations and optimizations in the Materials Project are carried out using VASP software \cite{kresse1996efficiency}. The selected materials are symbolically presented in Table \ref{tab:materials} with respective materials parameters used in the present work. The data set includes attributes such as chemical formula, space group symbol, space group number, formation energy ($E_f$), energy above hull ($E_H$), bandgap ($E_g$), number of atomic sites ($N_s$), density ($\rho$), unit cell volume (V) and crystal system.

\begin{figure}[h]
\centering
\includegraphics[width=0.7\textwidth]{barplot_for_number_of_crystal_system.png}
\caption{Bar plot showing the distribution of crystal systems}
\label{fig:bar_plt}
\end{figure}

The parameters $N_s$ and $\rho$ represent the number of atoms in a unit cell and the density of the bulk crystalline material, respectively. Machine learning models in this context often use V = M/$\rho$ (where M is atomic mass) as a primary variable. $E_H$ describes the energy required for the decomposition into the most stable phases \cite{jain2013jain}. All properties in the data set are calculated at 0 K and ambient pressure, with $E_g$ and V potentially influenced by temperature and pressure. However, these factors are held constant for this analysis. Fig. \ref{fig:bar_plt} shows the number of different crystallographic systems, i.e., monoclinic (978), triclinic (564), and orthorhombic (277) crystal systems in the present data set. Formation energy, energy above the hull, bandgap, sites, and volume were used as input features in the machine learning model with the crystal system serving as the output feature.

[... Additional sections continue in the same format ...]

\section{Conclusion}
This study demonstrated the effectiveness of machine learning models in classifying Li-P-(V, Mn, Fe, Co, Ni)-O-based cathode materials into three crystal systems (monoclinic, triclinic, and orthorhombic). Sequential Feature Selection (SFS) and Monte Carlo Cross-Validation (MCCV) are used as the key components in optimizing model performance and ensuring robust evaluation. The ensemble models like GBM, ERT, and RF achieved the highest accuracy of around 80.40\%, 78.96\%, and 80.69\%, respectively, using the top three selected features: Volume, Band gap, and Sites. This study highlights the power of ensemble methods and optimized feature selection for achieving reliable and robust predictions in crystal system classification tasks. Apart from this, the V-based phosphate-based materials are more stable than others.

\bibliographystyle{IEEEtran}
\bibliography{refernces}

\end{document}